const fs = require('fs');
const path = require('path');

// 统计已集成的节点数量
function countIntegratedNodes() {
  const panelsDir = 'editor/src/components/visual-script/panels';
  let totalIntegrated = 0;
  const panelStats = [];

  try {
    const panelFiles = fs.readdirSync(panelsDir).filter(file => 
      file.endsWith('.tsx') && file.includes('Panel')
    );

    for (const file of panelFiles) {
      const filePath = path.join(panelsDir, file);
      const content = fs.readFileSync(filePath, 'utf8');
      
      // 查找节点数量标记
      const countMatches = content.match(/count=\{(\d+)\}/g) || [];
      const badgeMatches = content.match(/Badge.*count.*(\d+)/g) || [];
      
      let nodeCount = 0;
      
      // 从count属性中提取数字
      for (const match of countMatches) {
        const num = parseInt(match.match(/\d+/)[0]);
        if (num > nodeCount) nodeCount = num;
      }
      
      // 从Badge组件中提取数字
      for (const match of badgeMatches) {
        const nums = match.match(/\d+/g);
        if (nums) {
          const num = parseInt(nums[nums.length - 1]);
          if (num > nodeCount) nodeCount = num;
        }
      }
      
      if (nodeCount > 0) {
        panelStats.push({
          panel: file,
          nodes: nodeCount
        });
        totalIntegrated += nodeCount;
      }
    }

    return { totalIntegrated, panelStats };
  } catch (error) {
    console.error('Error counting integrated nodes:', error);
    return { totalIntegrated: 0, panelStats: [] };
  }
}

// 统计已注册的节点数量
function countRegisteredNodes() {
  const registryDir = 'engine/src/visual-script/registry';
  let totalRegistered = 0;
  const registryStats = [];

  try {
    const registryFiles = fs.readdirSync(registryDir).filter(file => 
      file.endsWith('.ts') && file.includes('Registry') && !file.includes('.test.')
    );

    for (const file of registryFiles) {
      const filePath = path.join(registryDir, file);
      const content = fs.readFileSync(filePath, 'utf8');
      
      // 统计registerNode调用
      const registerMatches = content.match(/registerNode\(/g) || [];
      const nodeCount = registerMatches.length;
      
      if (nodeCount > 0) {
        registryStats.push({
          registry: file,
          nodes: nodeCount
        });
        totalRegistered += nodeCount;
      }
    }

    return { totalRegistered, registryStats };
  } catch (error) {
    console.error('Error counting registered nodes:', error);
    return { totalRegistered: 0, registryStats: [] };
  }
}

// 执行统计
const implementedResult = { count: 844, files: 173 }; // 从之前的扫描结果
const integratedResult = countIntegratedNodes();
const registeredResult = countRegisteredNodes();

console.log('=== DL引擎视觉脚本系统节点统计报告 ===');
console.log('');
console.log('📊 节点开发状态总览:');
console.log(`✅ 已实现节点: ${implementedResult.count}个 (100%)`);
console.log(`🔄 已注册节点: ${registeredResult.totalRegistered}个 (${((registeredResult.totalRegistered/implementedResult.count)*100).toFixed(1)}%)`);
console.log(`🟢 已集成节点: ${integratedResult.totalIntegrated}个 (${((integratedResult.totalIntegrated/implementedResult.count)*100).toFixed(1)}%)`);
console.log(`🔴 待集成节点: ${implementedResult.count - integratedResult.totalIntegrated}个 (${(((implementedResult.count - integratedResult.totalIntegrated)/implementedResult.count)*100).toFixed(1)}%)`);
console.log('');

console.log('📁 已集成面板详情:');
integratedResult.panelStats.forEach(panel => {
  console.log(`  ${panel.panel}: ${panel.nodes}个节点`);
});
console.log('');

console.log('📋 注册表状态:');
console.log(`  注册表文件数: ${registeredResult.registryStats.length}个`);
console.log(`  总注册节点数: ${registeredResult.totalRegistered}个`);
console.log('');

console.log('🎯 关键发现:');
console.log(`  - 节点实现完整度: 100% (${implementedResult.count}个节点)`);
console.log(`  - 节点注册完整度: ${((registeredResult.totalRegistered/implementedResult.count)*100).toFixed(1)}%`);
console.log(`  - 编辑器集成完整度: ${((integratedResult.totalIntegrated/implementedResult.count)*100).toFixed(1)}%`);
console.log(`  - 主要集成缺口: ${implementedResult.count - integratedResult.totalIntegrated}个节点待集成`);
